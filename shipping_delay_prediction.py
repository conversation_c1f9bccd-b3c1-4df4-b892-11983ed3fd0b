#!/usr/bin/env python3
"""
Shipping Delay Prediction using LightGBM
Dự đoán trạng thái delay cho các đơn hàng vận chuyển
Metric: Macro F1-Score
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import f1_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Load và chuẩn bị dữ liệu training"""
    print("Loading training data...")
    
    # Load các file training
    delay_4_6 = pd.read_csv('Dataset/delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    delay_7_9 = pd.read_csv('Dataset/delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_4_6 = pd.read_csv('Dataset/not_delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_7_9 = pd.read_csv('Dataset/not_delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    
    # Thêm label
    delay_4_6['label'] = 1  # delay
    delay_7_9['label'] = 1  # delay
    not_delay_4_6['label'] = 0  # not delay
    not_delay_7_9['label'] = 0  # not delay
    
    # Gộp tất cả dữ liệu training
    train_data = pd.concat([delay_4_6, delay_7_9, not_delay_4_6, not_delay_7_9], 
                          ignore_index=True)
    
    print(f"Training data shape: {train_data.shape}")
    print(f"Label distribution:\n{train_data['label'].value_counts()}")
    
    return train_data

def load_test_data():
    """Load dữ liệu test"""
    print("Loading test data...")
    test_data = pd.read_csv('Dataset/PILOT_10.csv')
    print(f"Test data shape: {test_data.shape}")
    return test_data

def preprocess_data(train_df, test_df):
    """Tiền xử lý dữ liệu"""
    print("Preprocessing data...")

    # Chuẩn hóa tên cột - xử lý vấn đề SPECIAL DIV vs SPECIAL_DIV
    print("Standardizing column names...")
    train_df = train_df.copy()
    test_df = test_df.copy()

    # Rename columns để tránh conflict
    if 'SPECIAL DIV' in train_df.columns and 'SPECIAL_DIV' in train_df.columns:
        print("Found both 'SPECIAL DIV' and 'SPECIAL_DIV' - renaming...")
        train_df = train_df.rename(columns={'SPECIAL DIV': 'SPECIAL_DIV_SPACE'})

    if 'SPECIAL DIV' in test_df.columns and 'SPECIAL_DIV' in test_df.columns:
        test_df = test_df.rename(columns={'SPECIAL DIV': 'SPECIAL_DIV_SPACE'})

    # Lấy các cột chung giữa train và test (trừ label)
    train_cols = set(train_df.columns) - {'label'}
    test_cols = set(test_df.columns) - {'ID'}
    common_cols = list(train_cols.intersection(test_cols))

    print(f"Common features: {len(common_cols)}")

    # Chỉ giữ lại các cột chung
    X_train = train_df[common_cols].copy()
    y_train = train_df['label'].copy()
    X_test = test_df[common_cols].copy()

    # Kiểm tra duplicate columns
    print("Checking for duplicate columns...")
    duplicate_cols = X_train.columns[X_train.columns.duplicated()].tolist()
    if duplicate_cols:
        print(f"Found duplicate columns: {duplicate_cols}")
        # Remove duplicate columns
        X_train = X_train.loc[:, ~X_train.columns.duplicated()]
        X_test = X_test.loc[:, ~X_test.columns.duplicated()]
        print(f"After removing duplicates - Train: {X_train.shape}, Test: {X_test.shape}")
    
    # Xử lý missing values
    print("Handling missing values...")
    
    # Xử lý datetime columns
    datetime_cols = []
    for col in X_train.columns:
        if 'date' in col.lower() or 'time' in col.lower():
            datetime_cols.append(col)
    
    print(f"Datetime columns: {datetime_cols}")
    
    # Convert datetime và extract features
    for col in datetime_cols:
        if col in X_train.columns:
            try:
                # Convert to datetime
                X_train[col] = pd.to_datetime(X_train[col], errors='coerce')
                X_test[col] = pd.to_datetime(X_test[col], errors='coerce')
                
                # Extract features
                X_train[f'{col}_year'] = X_train[col].dt.year
                X_train[f'{col}_month'] = X_train[col].dt.month
                X_train[f'{col}_day'] = X_train[col].dt.day
                X_train[f'{col}_dayofweek'] = X_train[col].dt.dayofweek
                
                X_test[f'{col}_year'] = X_test[col].dt.year
                X_test[f'{col}_month'] = X_test[col].dt.month
                X_test[f'{col}_day'] = X_test[col].dt.day
                X_test[f'{col}_dayofweek'] = X_test[col].dt.dayofweek
                
                # Drop original datetime column
                X_train = X_train.drop(col, axis=1)
                X_test = X_test.drop(col, axis=1)
                
            except Exception as e:
                print(f"Error processing {col}: {e}")
    
    # Xử lý categorical columns
    categorical_cols = []
    for col in X_train.columns:
        if X_train[col].dtype == 'object':
            categorical_cols.append(col)
    
    print(f"Categorical columns: {len(categorical_cols)}")
    
    # Label encoding cho categorical variables
    label_encoders = {}
    for col in categorical_cols:
        le = LabelEncoder()
        
        # Combine train and test for consistent encoding
        combined_values = pd.concat([X_train[col].astype(str), X_test[col].astype(str)])
        le.fit(combined_values.fillna('missing'))
        
        X_train[col] = le.transform(X_train[col].astype(str).fillna('missing'))
        X_test[col] = le.transform(X_test[col].astype(str).fillna('missing'))
        
        label_encoders[col] = le
    
    # Fill missing values cho numeric columns (memory efficient)
    for col in X_train.columns:
        if X_train[col].dtype in ['int64', 'float64']:
            median_val = X_train[col].median()
            X_train[col] = X_train[col].fillna(median_val)
            X_test[col] = X_test[col].fillna(median_val)
    
    print(f"Final feature shape - Train: {X_train.shape}, Test: {X_test.shape}")
    
    return X_train, y_train, X_test, label_encoders

def train_lightgbm_model(X_train, y_train):
    """Training LightGBM model với cross-validation"""
    print("Training LightGBM model...")

    # LightGBM parameters
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42
    }
    
    # Cross-validation với Macro F1-Score
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    cv_scores = []
    models = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
        print(f"Training fold {fold + 1}/5...")
        
        X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        # Create LightGBM datasets
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        # Train model
        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
        )
        
        # Predict và tính F1-score
        val_pred = model.predict(X_fold_val)
        val_pred_binary = (val_pred > 0.5).astype(int)

        f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
        cv_scores.append(f1_macro)
        models.append(model)

        print(f"Fold {fold + 1} Macro F1-Score: {f1_macro:.4f}")
    
    print(f"Average CV Macro F1-Score: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores)*2:.4f})")
    
    return models, cv_scores

def make_predictions(models, X_test, test_df):
    """Tạo predictions và file submission"""
    print("Making predictions...")

    # Average predictions từ tất cả models
    predictions = np.zeros(len(X_test))

    for model in models:
        pred = model.predict(X_test)
        predictions += pred

    predictions /= len(models)

    # Convert to binary predictions
    binary_predictions = (predictions > 0.5).astype(int)

    # Tạo submission file
    submission = pd.DataFrame({
        'ID': test_df['ID'],
        'label': binary_predictions
    })

    submission.to_csv('submission.csv', index=False)
    print("Submission file saved as 'submission.csv'")

    print(f"Prediction distribution:\n{pd.Series(binary_predictions).value_counts()}")

    return submission

def main():
    """Main function"""
    print("=== Shipping Delay Prediction ===")
    
    # Load data
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    
    # Preprocess
    X_train, y_train, X_test, label_encoders = preprocess_data(train_df, test_df)
    
    # Train model
    models, cv_scores = train_lightgbm_model(X_train, y_train)
    
    # Make predictions
    submission = make_predictions(models, X_test, test_df)
    
    print("=== Completed ===")
    print(f"Final CV Score: {np.mean(cv_scores):.4f}")
    
    return submission

if __name__ == "__main__":
    submission = main()
