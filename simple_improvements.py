#!/usr/bin/env python3
"""
Simple improvements to the existing model
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import f1_score
import warnings
warnings.filterwarnings('ignore')

def add_simple_improvements(X_train, X_test, y_train):
    """Thêm các cải tiến đơn giản"""
    print("Adding simple improvements...")
    
    # 1. Target encoding cho categorical variables
    categorical_cols = [col for col in X_train.columns if X_train[col].dtype == 'object']
    
    for col in categorical_cols:
        # Target encoding với smoothing
        target_mean = X_train.groupby(col)[y_train].mean()
        global_mean = y_train.mean()
        
        X_train[f'{col}_target_enc'] = X_train[col].map(target_mean).fillna(global_mean)
        X_test[f'{col}_target_enc'] = X_test[col].map(target_mean).fillna(global_mean)
        
        # Frequency encoding
        freq_map = X_train[col].value_counts().to_dict()
        X_train[f'{col}_freq'] = X_train[col].map(freq_map).fillna(0)
        X_test[f'{col}_freq'] = X_test[col].map(freq_map).fillna(0)
    
    # 2. Datetime features nâng cao
    datetime_cols = [col for col in X_train.columns if 'date' in col.lower() or 'time' in col.lower()]
    
    for col in datetime_cols:
        if col in X_train.columns:
            try:
                X_train[col] = pd.to_datetime(X_train[col], errors='coerce')
                X_test[col] = pd.to_datetime(X_test[col], errors='coerce')
                
                # Thêm features
                X_train[f'{col}_quarter'] = X_train[col].dt.quarter
                X_train[f'{col}_is_weekend'] = (X_train[col].dt.dayofweek >= 5).astype(int)
                X_train[f'{col}_week_of_year'] = X_train[col].dt.isocalendar().week
                
                X_test[f'{col}_quarter'] = X_test[col].dt.quarter
                X_test[f'{col}_is_weekend'] = (X_test[col].dt.dayofweek >= 5).astype(int)
                X_test[f'{col}_week_of_year'] = X_test[col].dt.isocalendar().week
                
            except Exception as e:
                print(f"Error processing {col}: {e}")
    
    # 3. Interaction features
    if 'SUPPLIER' in X_train.columns and 'PRODUCT' in X_train.columns:
        X_train['SUPPLIER_PRODUCT'] = X_train['SUPPLIER'] * 1000 + X_train['PRODUCT']
        X_test['SUPPLIER_PRODUCT'] = X_test['SUPPLIER'] * 1000 + X_test['PRODUCT']
    
    print(f"After improvements - Train: {X_train.shape}, Test: {X_test.shape}")
    return X_train, X_test

def train_with_threshold_tuning(X_train, y_train):
    """Training với threshold tuning"""
    print("Training with threshold tuning...")
    
    # Tính class weights
    class_counts = y_train.value_counts()
    scale_pos_weight = class_counts[0] / class_counts[1]
    
    # LightGBM parameters với class balancing
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'scale_pos_weight': scale_pos_weight,  # Xử lý imbalance
        'verbose': -1,
        'random_state': 42
    }
    
    # Cross-validation với threshold tuning
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    cv_scores = []
    models = []
    optimal_thresholds = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
        print(f"Training fold {fold + 1}/5...")
        
        X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        # Train model
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
        )
        
        # Threshold tuning
        val_pred_proba = model.predict(X_fold_val)
        
        best_threshold = 0.5
        best_f1 = 0
        
        # Tìm threshold tối ưu
        for threshold in np.arange(0.1, 0.9, 0.05):
            val_pred_binary = (val_pred_proba > threshold).astype(int)
            f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
            
            if f1_macro > best_f1:
                best_f1 = f1_macro
                best_threshold = threshold
        
        cv_scores.append(best_f1)
        models.append(model)
        optimal_thresholds.append(best_threshold)
        
        print(f"Fold {fold + 1} - Threshold: {best_threshold:.3f}, F1: {best_f1:.4f}")
    
    avg_threshold = np.mean(optimal_thresholds)
    print(f"Average CV F1-Score: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores)*2:.4f})")
    print(f"Average optimal threshold: {avg_threshold:.3f}")
    
    return models, cv_scores, avg_threshold

def make_improved_predictions(models, X_test, test_df, threshold):
    """Predictions với optimal threshold"""
    print("Making improved predictions...")
    
    predictions = np.zeros(len(X_test))
    
    for model in models:
        pred = model.predict(X_test)
        predictions += pred
    
    predictions /= len(models)
    
    # Apply threshold
    binary_predictions = (predictions > threshold).astype(int)
    
    submission = pd.DataFrame({
        'ID': test_df['ID'],
        'label': binary_predictions
    })
    
    submission.to_csv('submission_improved_simple.csv', index=False)
    print("Improved submission saved as 'submission_improved_simple.csv'")
    
    print(f"Threshold used: {threshold:.3f}")
    print(f"Prediction distribution:\n{pd.Series(binary_predictions).value_counts()}")
    
    return submission

def main():
    """Main function"""
    print("=== Simple Improvements ===")
    
    # Load data
    from shipping_delay_prediction import load_and_prepare_data, load_test_data, preprocess_data
    
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    
    # Basic preprocessing
    X_train, y_train, X_test, _ = preprocess_data(train_df, test_df)
    
    # Add improvements
    X_train, X_test = add_simple_improvements(X_train, X_test, y_train)
    
    # Train with improvements
    models, cv_scores, threshold = train_with_threshold_tuning(X_train, y_train)
    
    # Make predictions
    submission = make_improved_predictions(models, X_test, test_df, threshold)
    
    print("=== Completed ===")
    print(f"Improved CV Score: {np.mean(cv_scores):.4f}")
    
    return submission

if __name__ == "__main__":
    submission = main()
