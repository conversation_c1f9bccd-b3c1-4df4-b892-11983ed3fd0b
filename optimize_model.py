#!/usr/bin/env python3
"""
Tối ưu hóa model với hyperparameter tuning và feature engineering nâng cao
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import f1_score
import optuna
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Load và chuẩn bị dữ liệu training"""
    print("Loading training data...")
    
    # Load các file training
    delay_4_6 = pd.read_csv('Dataset/delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    delay_7_9 = pd.read_csv('Dataset/delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_4_6 = pd.read_csv('Dataset/not_delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_7_9 = pd.read_csv('Dataset/not_delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    
    # Thêm label
    delay_4_6['label'] = 1
    delay_7_9['label'] = 1
    not_delay_4_6['label'] = 0
    not_delay_7_9['label'] = 0
    
    # Gộp tất cả dữ liệu training
    train_data = pd.concat([delay_4_6, delay_7_9, not_delay_4_6, not_delay_7_9], 
                          ignore_index=True)
    
    print(f"Training data shape: {train_data.shape}")
    print(f"Label distribution:\n{train_data['label'].value_counts()}")
    
    return train_data

def advanced_feature_engineering(X_train, X_test):
    """Feature engineering nâng cao"""
    print("Advanced feature engineering...")
    
    # Tạo interaction features cho các cột quan trọng
    important_cols = ['SUPPLIER', 'PRODUCT', 'CONDITION', 'SPECIAL_DIV']
    
    for i, col1 in enumerate(important_cols):
        for col2 in important_cols[i+1:]:
            if col1 in X_train.columns and col2 in X_train.columns:
                # Interaction feature
                X_train[f'{col1}_{col2}_interaction'] = X_train[col1].astype(str) + '_' + X_train[col2].astype(str)
                X_test[f'{col1}_{col2}_interaction'] = X_test[col1].astype(str) + '_' + X_test[col2].astype(str)
                
                # Label encode interaction
                le = LabelEncoder()
                combined = pd.concat([X_train[f'{col1}_{col2}_interaction'], X_test[f'{col1}_{col2}_interaction']])
                le.fit(combined.fillna('missing'))
                
                X_train[f'{col1}_{col2}_interaction'] = le.transform(X_train[f'{col1}_{col2}_interaction'].fillna('missing'))
                X_test[f'{col1}_{col2}_interaction'] = le.transform(X_test[f'{col1}_{col2}_interaction'].fillna('missing'))
    
    # Aggregation features
    if 'SUPPLIER' in X_train.columns:
        # Count encoding
        supplier_counts = X_train['SUPPLIER'].value_counts().to_dict()
        X_train['SUPPLIER_count'] = X_train['SUPPLIER'].map(supplier_counts).fillna(0)
        X_test['SUPPLIER_count'] = X_test['SUPPLIER'].map(supplier_counts).fillna(0)
    
    if 'PRODUCT' in X_train.columns:
        product_counts = X_train['PRODUCT'].value_counts().to_dict()
        X_train['PRODUCT_count'] = X_train['PRODUCT'].map(product_counts).fillna(0)
        X_test['PRODUCT_count'] = X_test['PRODUCT'].map(product_counts).fillna(0)
    
    print(f"After feature engineering - Train: {X_train.shape}, Test: {X_test.shape}")
    return X_train, X_test

def objective(trial, X_train, y_train):
    """Objective function cho Optuna hyperparameter tuning"""
    
    # Hyperparameters to tune
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': trial.suggest_int('num_leaves', 10, 100),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
        'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
        'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
        'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
        'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
        'verbose': -1,
        'random_state': 42
    }
    
    # Cross-validation
    skf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)  # Reduced folds for speed
    cv_scores = []
    
    for train_idx, val_idx in skf.split(X_train, y_train):
        X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=500,  # Reduced for speed
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
        )
        
        val_pred = model.predict(X_fold_val)
        val_pred_binary = (val_pred > 0.5).astype(int)
        f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
        cv_scores.append(f1_macro)
    
    return np.mean(cv_scores)

def optimize_hyperparameters(X_train, y_train):
    """Tối ưu hyperparameters với Optuna"""
    print("Optimizing hyperparameters...")
    
    study = optuna.create_study(direction='maximize')
    study.optimize(lambda trial: objective(trial, X_train, y_train), n_trials=20)  # Reduced trials
    
    print(f"Best score: {study.best_value:.4f}")
    print(f"Best params: {study.best_params}")
    
    return study.best_params

def main():
    """Main function với optimization"""
    print("=== Advanced Shipping Delay Prediction ===")
    
    # Load data (reuse preprocessing from main script)
    from shipping_delay_prediction import load_test_data, preprocess_data
    
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    X_train, y_train, X_test, _ = preprocess_data(train_df, test_df)
    
    # Advanced feature engineering
    X_train, X_test = advanced_feature_engineering(X_train, X_test)
    
    # Hyperparameter optimization
    best_params = optimize_hyperparameters(X_train, y_train)
    
    # Train final model with best params
    print("Training final model with optimized parameters...")
    best_params.update({
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'verbose': -1,
        'random_state': 42
    })
    
    # Final cross-validation
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    models = []
    cv_scores = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
        print(f"Training final fold {fold + 1}/5...")
        
        X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        model = lgb.train(
            best_params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
        )
        
        val_pred = model.predict(X_fold_val)
        val_pred_binary = (val_pred > 0.5).astype(int)
        f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
        cv_scores.append(f1_macro)
        models.append(model)
        
        print(f"Fold {fold + 1} Macro F1-Score: {f1_macro:.4f}")
    
    print(f"Optimized CV Macro F1-Score: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores)*2:.4f})")
    
    # Make predictions
    print("Making optimized predictions...")
    predictions = np.zeros(len(X_test))
    
    for model in models:
        pred = model.predict(X_test)
        predictions += pred
    
    predictions /= len(models)
    binary_predictions = (predictions > 0.5).astype(int)
    
    # Save optimized submission
    test_df = load_test_data()
    submission = pd.DataFrame({
        'ID': test_df['ID'],
        'label': binary_predictions
    })
    
    submission.to_csv('submission_optimized.csv', index=False)
    print("Optimized submission saved as 'submission_optimized.csv'")
    
    print(f"Optimized prediction distribution:\n{pd.Series(binary_predictions).value_counts()}")
    print(f"Final optimized CV Score: {np.mean(cv_scores):.4f}")

if __name__ == "__main__":
    main()
