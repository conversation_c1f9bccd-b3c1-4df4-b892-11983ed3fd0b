#!/usr/bin/env python3
"""
Enhanced Shipping Delay Prediction với các cải tiến chính
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import f1_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def enhanced_feature_engineering(X_train, X_test, y_train):
    """Feature engineering nâng cao"""
    print("Enhanced feature engineering...")
    
    # 1. Target encoding cho categorical variables
    categorical_cols = [col for col in X_train.columns if X_train[col].dtype == 'object']
    
    for col in categorical_cols:
        # Target encoding
        target_mean = X_train.groupby(col)[y_train].mean()
        global_mean = y_train.mean()
        
        # Smoothing để tránh overfitting
        counts = X_train.groupby(col).size()
        smooth_factor = 100  # C<PERSON> thể điều chỉnh
        
        smoothed_target = (target_mean * counts + global_mean * smooth_factor) / (counts + smooth_factor)
        
        X_train[f'{col}_target_enc'] = X_train[col].map(smoothed_target)
        X_test[f'{col}_target_enc'] = X_test[col].map(smoothed_target)
        
        # Fill missing với global mean
        X_train[f'{col}_target_enc'] = X_train[f'{col}_target_enc'].fillna(global_mean)
        X_test[f'{col}_target_enc'] = X_test[f'{col}_target_enc'].fillna(global_mean)
        
        # Frequency encoding
        freq_map = X_train[col].value_counts().to_dict()
        X_train[f'{col}_freq'] = X_train[col].map(freq_map).fillna(0)
        X_test[f'{col}_freq'] = X_test[col].map(freq_map).fillna(0)
        
        # Label encoding (giữ lại cho model)
        le = LabelEncoder()
        combined_values = pd.concat([X_train[col].astype(str), X_test[col].astype(str)])
        le.fit(combined_values.fillna('missing'))
        
        X_train[col] = le.transform(X_train[col].astype(str).fillna('missing'))
        X_test[col] = le.transform(X_test[col].astype(str).fillna('missing'))
    
    # 2. Datetime features nâng cao
    datetime_cols = [col for col in X_train.columns if 'date' in col.lower() or 'time' in col.lower()]
    
    for col in datetime_cols:
        if col in X_train.columns:
            try:
                X_train[col] = pd.to_datetime(X_train[col], errors='coerce')
                X_test[col] = pd.to_datetime(X_test[col], errors='coerce')
                
                # Nhiều datetime features
                X_train[f'{col}_year'] = X_train[col].dt.year
                X_train[f'{col}_month'] = X_train[col].dt.month
                X_train[f'{col}_day'] = X_train[col].dt.day
                X_train[f'{col}_dayofweek'] = X_train[col].dt.dayofweek
                X_train[f'{col}_quarter'] = X_train[col].dt.quarter
                X_train[f'{col}_is_weekend'] = (X_train[col].dt.dayofweek >= 5).astype(int)
                X_train[f'{col}_week_of_year'] = X_train[col].dt.isocalendar().week
                
                X_test[f'{col}_year'] = X_test[col].dt.year
                X_test[f'{col}_month'] = X_test[col].dt.month
                X_test[f'{col}_day'] = X_test[col].dt.day
                X_test[f'{col}_dayofweek'] = X_test[col].dt.dayofweek
                X_test[f'{col}_quarter'] = X_test[col].dt.quarter
                X_test[f'{col}_is_weekend'] = (X_test[col].dt.dayofweek >= 5).astype(int)
                X_test[f'{col}_week_of_year'] = X_test[col].dt.isocalendar().week
                
                X_train = X_train.drop(col, axis=1)
                X_test = X_test.drop(col, axis=1)
                
            except Exception as e:
                print(f"Error processing {col}: {e}")
    
    # 3. Interaction features
    important_cols = ['SUPPLIER', 'PRODUCT', 'CONDITION']
    for i, col1 in enumerate(important_cols):
        for col2 in important_cols[i+1:]:
            if col1 in X_train.columns and col2 in X_train.columns:
                X_train[f'{col1}_{col2}_interaction'] = X_train[col1] * 1000 + X_train[col2]
                X_test[f'{col1}_{col2}_interaction'] = X_test[col1] * 1000 + X_test[col2]
    
    # 4. Statistical features
    numeric_cols = X_train.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 1:
        X_train['numeric_sum'] = X_train[numeric_cols].sum(axis=1)
        X_train['numeric_mean'] = X_train[numeric_cols].mean(axis=1)
        X_train['numeric_std'] = X_train[numeric_cols].std(axis=1)
        
        X_test['numeric_sum'] = X_test[numeric_cols].sum(axis=1)
        X_test['numeric_mean'] = X_test[numeric_cols].mean(axis=1)
        X_test['numeric_std'] = X_test[numeric_cols].std(axis=1)
    
    # Fill missing values
    for col in X_train.columns:
        if X_train[col].dtype in ['int64', 'float64']:
            median_val = X_train[col].median()
            X_train[col] = X_train[col].fillna(median_val)
            X_test[col] = X_test[col].fillna(median_val)
    
    print(f"After enhanced feature engineering - Train: {X_train.shape}, Test: {X_test.shape}")
    return X_train, X_test

def train_enhanced_model(X_train, y_train):
    """Training model với class weights và threshold tuning"""
    print("Training enhanced model...")
    
    # Tính class weights
    class_counts = y_train.value_counts()
    total_samples = len(y_train)
    class_weight = {
        0: total_samples / (2 * class_counts[0]),
        1: total_samples / (2 * class_counts[1])
    }
    
    print(f"Class weights: {class_weight}")
    
    # Enhanced LightGBM parameters
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'min_child_samples': 20,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'class_weight': class_weight,
        'verbose': -1,
        'random_state': 42
    }
    
    # Cross-validation với threshold tuning
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    cv_scores = []
    models = []
    optimal_thresholds = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
        print(f"Training fold {fold + 1}/5...")
        
        X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        # Create LightGBM datasets với class weights
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        # Train model
        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
        )
        
        # Threshold tuning
        val_pred_proba = model.predict(X_fold_val)
        
        best_threshold = 0.5
        best_f1 = 0
        
        for threshold in np.arange(0.1, 0.9, 0.02):
            val_pred_binary = (val_pred_proba > threshold).astype(int)
            f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
            
            if f1_macro > best_f1:
                best_f1 = f1_macro
                best_threshold = threshold
        
        cv_scores.append(best_f1)
        models.append(model)
        optimal_thresholds.append(best_threshold)
        
        print(f"Fold {fold + 1} - Optimal threshold: {best_threshold:.3f}, Macro F1-Score: {best_f1:.4f}")
    
    avg_threshold = np.mean(optimal_thresholds)
    print(f"Average CV Macro F1-Score: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores)*2:.4f})")
    print(f"Average optimal threshold: {avg_threshold:.3f}")
    
    return models, cv_scores, avg_threshold

def make_enhanced_predictions(models, X_test, test_df, threshold):
    """Tạo predictions với optimal threshold"""
    print("Making enhanced predictions...")
    
    # Ensemble predictions
    predictions = np.zeros(len(X_test))
    
    for model in models:
        pred = model.predict(X_test)
        predictions += pred
    
    predictions /= len(models)
    
    # Apply optimal threshold
    binary_predictions = (predictions > threshold).astype(int)
    
    # Tạo submission file
    submission = pd.DataFrame({
        'ID': test_df['ID'],
        'label': binary_predictions
    })
    
    submission.to_csv('submission_enhanced.csv', index=False)
    print("Enhanced submission file saved as 'submission_enhanced.csv'")
    
    print(f"Used threshold: {threshold:.3f}")
    print(f"Prediction distribution:\n{pd.Series(binary_predictions).value_counts()}")
    print(f"Prediction probabilities - Mean: {predictions.mean():.4f}, Std: {predictions.std():.4f}")
    
    return submission

def main():
    """Main function với enhancements"""
    print("=== Enhanced Shipping Delay Prediction ===")
    
    # Load data
    from shipping_delay_prediction import load_and_prepare_data, load_test_data, preprocess_data
    
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    
    # Basic preprocessing
    X_train, y_train, X_test, _ = preprocess_data(train_df, test_df)
    
    # Enhanced feature engineering
    X_train, X_test = enhanced_feature_engineering(X_train, X_test, y_train)
    
    # Train enhanced model
    models, cv_scores, optimal_threshold = train_enhanced_model(X_train, y_train)
    
    # Make predictions
    submission = make_enhanced_predictions(models, X_test, test_df, optimal_threshold)
    
    print("=== Completed ===")
    print(f"Final Enhanced CV Score: {np.mean(cv_scores):.4f}")
    
    return submission

if __name__ == "__main__":
    submission = main()
