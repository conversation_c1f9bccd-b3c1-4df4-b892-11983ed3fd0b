#!/usr/bin/env python3
"""
Improved Shipping Delay Prediction với nhiều cải tiến
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import f1_score, classification_report
from sklearn.feature_selection import SelectKBest, f_classif
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline as ImbPipeline
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Load và chuẩn bị dữ liệu training với thông tin thêm"""
    print("Loading training data...")
    
    # Load các file training
    delay_4_6 = pd.read_csv('Dataset/delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    delay_7_9 = pd.read_csv('Dataset/delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_4_6 = pd.read_csv('Dataset/not_delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
    not_delay_7_9 = pd.read_csv('Dataset/not_delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
    
    # Thêm label và delay_type để phân biệt
    delay_4_6['label'] = 1
    delay_4_6['delay_type'] = 'delay_4_6'
    delay_7_9['label'] = 1  
    delay_7_9['delay_type'] = 'delay_7_9'
    not_delay_4_6['label'] = 0
    not_delay_4_6['delay_type'] = 'not_delay_4_6'
    not_delay_7_9['label'] = 0
    not_delay_7_9['delay_type'] = 'not_delay_7_9'
    
    # Gộp tất cả dữ liệu training
    train_data = pd.concat([delay_4_6, delay_7_9, not_delay_4_6, not_delay_7_9], 
                          ignore_index=True)
    
    print(f"Training data shape: {train_data.shape}")
    print(f"Label distribution:\n{train_data['label'].value_counts()}")
    print(f"Delay type distribution:\n{train_data['delay_type'].value_counts()}")
    
    return train_data

def advanced_feature_engineering(X_train, X_test, y_train=None):
    """Feature engineering nâng cao với nhiều kỹ thuật"""
    print("Advanced feature engineering...")
    
    # 1. Datetime features nâng cao
    datetime_cols = [col for col in X_train.columns if 'date' in col.lower() or 'time' in col.lower()]
    
    for col in datetime_cols:
        if col in X_train.columns:
            try:
                X_train[col] = pd.to_datetime(X_train[col], errors='coerce')
                X_test[col] = pd.to_datetime(X_test[col], errors='coerce')
                
                # Thêm nhiều datetime features
                X_train[f'{col}_year'] = X_train[col].dt.year
                X_train[f'{col}_month'] = X_train[col].dt.month
                X_train[f'{col}_day'] = X_train[col].dt.day
                X_train[f'{col}_dayofweek'] = X_train[col].dt.dayofweek
                X_train[f'{col}_quarter'] = X_train[col].dt.quarter
                X_train[f'{col}_is_weekend'] = (X_train[col].dt.dayofweek >= 5).astype(int)
                X_train[f'{col}_is_month_start'] = X_train[col].dt.is_month_start.astype(int)
                X_train[f'{col}_is_month_end'] = X_train[col].dt.is_month_end.astype(int)
                
                X_test[f'{col}_year'] = X_test[col].dt.year
                X_test[f'{col}_month'] = X_test[col].dt.month
                X_test[f'{col}_day'] = X_test[col].dt.day
                X_test[f'{col}_dayofweek'] = X_test[col].dt.dayofweek
                X_test[f'{col}_quarter'] = X_test[col].dt.quarter
                X_test[f'{col}_is_weekend'] = (X_test[col].dt.dayofweek >= 5).astype(int)
                X_test[f'{col}_is_month_start'] = X_test[col].dt.is_month_start.astype(int)
                X_test[f'{col}_is_month_end'] = X_test[col].dt.is_month_end.astype(int)
                
                X_train = X_train.drop(col, axis=1)
                X_test = X_test.drop(col, axis=1)
                
            except Exception as e:
                print(f"Error processing {col}: {e}")
    
    # 2. Categorical encoding với target encoding
    categorical_cols = [col for col in X_train.columns if X_train[col].dtype == 'object']
    
    for col in categorical_cols:
        # Target encoding (chỉ khi có y_train)
        if y_train is not None:
            target_mean = X_train.groupby(col)[y_train.name].mean()
            X_train[f'{col}_target_enc'] = X_train[col].map(target_mean)
            X_test[f'{col}_target_enc'] = X_test[col].map(target_mean)
            
            # Fill missing với overall mean
            overall_mean = y_train.mean()
            X_train[f'{col}_target_enc'] = X_train[f'{col}_target_enc'].fillna(overall_mean)
            X_test[f'{col}_target_enc'] = X_test[f'{col}_target_enc'].fillna(overall_mean)
        
        # Frequency encoding
        freq_map = X_train[col].value_counts().to_dict()
        X_train[f'{col}_freq'] = X_train[col].map(freq_map).fillna(0)
        X_test[f'{col}_freq'] = X_test[col].map(freq_map).fillna(0)
        
        # Label encoding
        le = LabelEncoder()
        combined_values = pd.concat([X_train[col].astype(str), X_test[col].astype(str)])
        le.fit(combined_values.fillna('missing'))
        
        X_train[col] = le.transform(X_train[col].astype(str).fillna('missing'))
        X_test[col] = le.transform(X_test[col].astype(str).fillna('missing'))
    
    # 3. Interaction features
    important_cols = ['SUPPLIER', 'PRODUCT', 'CONDITION']
    for i, col1 in enumerate(important_cols):
        for col2 in important_cols[i+1:]:
            if col1 in X_train.columns and col2 in X_train.columns:
                X_train[f'{col1}_{col2}_interaction'] = X_train[col1] * 1000 + X_train[col2]
                X_test[f'{col1}_{col2}_interaction'] = X_test[col1] * 1000 + X_test[col2]
    
    # 4. Statistical features
    numeric_cols = X_train.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 1:
        X_train['numeric_mean'] = X_train[numeric_cols].mean(axis=1)
        X_train['numeric_std'] = X_train[numeric_cols].std(axis=1)
        X_train['numeric_max'] = X_train[numeric_cols].max(axis=1)
        X_train['numeric_min'] = X_train[numeric_cols].min(axis=1)
        
        X_test['numeric_mean'] = X_test[numeric_cols].mean(axis=1)
        X_test['numeric_std'] = X_test[numeric_cols].std(axis=1)
        X_test['numeric_max'] = X_test[numeric_cols].max(axis=1)
        X_test['numeric_min'] = X_test[numeric_cols].min(axis=1)
    
    # Fill missing values
    for col in X_train.columns:
        if X_train[col].dtype in ['int64', 'float64']:
            median_val = X_train[col].median()
            X_train[col] = X_train[col].fillna(median_val)
            X_test[col] = X_test[col].fillna(median_val)
    
    print(f"After feature engineering - Train: {X_train.shape}, Test: {X_test.shape}")
    return X_train, X_test

def handle_class_imbalance(X_train, y_train, strategy='combined'):
    """Xử lý class imbalance"""
    print(f"Original distribution: {y_train.value_counts().to_dict()}")
    
    if strategy == 'smote':
        # Chỉ SMOTE
        smote = SMOTE(random_state=42, k_neighbors=3)
        X_resampled, y_resampled = smote.fit_resample(X_train, y_train)
        
    elif strategy == 'combined':
        # Kết hợp SMOTE và undersampling
        over = SMOTE(sampling_strategy=0.1, random_state=42, k_neighbors=3)  # Tăng minority lên 10%
        under = RandomUnderSampler(sampling_strategy=0.2, random_state=42)   # Giảm majority xuống 20%
        
        pipeline = ImbPipeline([
            ('over', over),
            ('under', under)
        ])
        
        X_resampled, y_resampled = pipeline.fit_resample(X_train, y_train)
        
    else:
        # Không resampling
        X_resampled, y_resampled = X_train, y_train
    
    print(f"After resampling: {pd.Series(y_resampled).value_counts().to_dict()}")
    return X_resampled, y_resampled

def train_ensemble_model(X_train, y_train):
    """Training ensemble model với multiple algorithms"""
    print("Training ensemble model...")
    
    # Xử lý class imbalance
    X_resampled, y_resampled = handle_class_imbalance(X_train, y_train, strategy='combined')
    
    # LightGBM parameters với class weight
    lgb_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'min_child_samples': 20,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'is_unbalance': True,  # Tự động xử lý imbalance
        'verbose': -1,
        'random_state': 42
    }
    
    # Cross-validation
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    cv_scores = []
    models = []
    feature_importance = np.zeros(X_resampled.shape[1])
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_resampled, y_resampled)):
        print(f"Training fold {fold + 1}/5...")
        
        X_fold_train = X_resampled.iloc[train_idx] if hasattr(X_resampled, 'iloc') else X_resampled[train_idx]
        X_fold_val = X_resampled.iloc[val_idx] if hasattr(X_resampled, 'iloc') else X_resampled[val_idx]
        y_fold_train = y_resampled.iloc[train_idx] if hasattr(y_resampled, 'iloc') else y_resampled[train_idx]
        y_fold_val = y_resampled.iloc[val_idx] if hasattr(y_resampled, 'iloc') else y_resampled[val_idx]
        
        # Train LightGBM
        train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
        val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
        
        model = lgb.train(
            lgb_params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
        )
        
        # Feature importance
        feature_importance += model.feature_importance(importance_type='gain')
        
        # Predict với threshold tuning
        val_pred_proba = model.predict(X_fold_val)
        
        # Tìm threshold tối ưu
        best_threshold = 0.5
        best_f1 = 0
        
        for threshold in np.arange(0.1, 0.9, 0.05):
            val_pred_binary = (val_pred_proba > threshold).astype(int)
            f1_macro = f1_score(y_fold_val, val_pred_binary, average='macro')
            
            if f1_macro > best_f1:
                best_f1 = f1_macro
                best_threshold = threshold
        
        cv_scores.append(best_f1)
        models.append((model, best_threshold))
        
        print(f"Fold {fold + 1} - Best threshold: {best_threshold:.3f}, Macro F1-Score: {best_f1:.4f}")
    
    print(f"Average CV Macro F1-Score: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores)*2:.4f})")
    
    # Feature importance analysis
    feature_names = X_train.columns if hasattr(X_train, 'columns') else [f'feature_{i}' for i in range(X_train.shape[1])]
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance / len(models)
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 most important features:")
    print(feature_importance_df.head(10))
    
    return models, cv_scores, feature_importance_df

def make_improved_predictions(models, X_test, test_df):
    """Tạo predictions với ensemble và threshold tuning"""
    print("Making improved predictions...")
    
    # Ensemble predictions
    predictions = np.zeros(len(X_test))
    
    for model, threshold in models:
        pred_proba = model.predict(X_test)
        predictions += pred_proba
    
    predictions /= len(models)
    
    # Sử dụng average threshold từ CV
    avg_threshold = np.mean([threshold for _, threshold in models])
    binary_predictions = (predictions > avg_threshold).astype(int)
    
    print(f"Average optimal threshold: {avg_threshold:.3f}")
    
    # Tạo submission file
    submission = pd.DataFrame({
        'ID': test_df['ID'],
        'label': binary_predictions
    })
    
    submission.to_csv('submission_improved.csv', index=False)
    print("Improved submission file saved as 'submission_improved.csv'")
    
    print(f"Prediction distribution:\n{pd.Series(binary_predictions).value_counts()}")
    print(f"Prediction probabilities stats:")
    print(f"Mean: {predictions.mean():.4f}, Std: {predictions.std():.4f}")
    print(f"Min: {predictions.min():.4f}, Max: {predictions.max():.4f}")
    
    return submission, predictions

def main():
    """Main function với improvements"""
    print("=== Improved Shipping Delay Prediction ===")
    
    # Load data
    from shipping_delay_prediction import load_test_data, preprocess_data
    
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    
    # Basic preprocessing
    X_train, y_train, X_test, _ = preprocess_data(train_df, test_df)
    
    # Advanced feature engineering
    X_train, X_test = advanced_feature_engineering(X_train, X_test, y_train)
    
    # Train improved model
    models, cv_scores, feature_importance = train_ensemble_model(X_train, y_train)
    
    # Make predictions
    submission, predictions = make_improved_predictions(models, X_test, test_df)
    
    print("=== Completed ===")
    print(f"Final Improved CV Score: {np.mean(cv_scores):.4f}")
    
    return submission, feature_importance

if __name__ == "__main__":
    submission, feature_importance = main()
