#!/usr/bin/env python3
"""
Debug script for improved model
"""

try:
    import pandas as pd
    import numpy as np
    import lightgbm as lgb
    from sklearn.model_selection import StratifiedKFold
    from sklearn.preprocessing import LabelEncoder, StandardScaler
    from sklearn.metrics import f1_score, classification_report
    from sklearn.feature_selection import SelectKBest, f_classif
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import RandomUnderSampler
    from imblearn.pipeline import Pipeline as ImbPipeline
    print("All imports successful!")
    
    # Test basic functionality
    from shipping_delay_prediction import load_and_prepare_data, load_test_data, preprocess_data
    
    print("Loading data...")
    train_df = load_and_prepare_data()
    test_df = load_test_data()
    
    print("Preprocessing...")
    X_train, y_train, X_test, _ = preprocess_data(train_df, test_df)
    
    print(f"Data shapes - Train: {X_train.shape}, Test: {X_test.shape}")
    print("Basic setup successful!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
