import pandas as pd

# Load data
print("Loading data...")
delay_4_6 = pd.read_csv('Dataset/delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
delay_7_9 = pd.read_csv('Dataset/delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
not_delay_4_6 = pd.read_csv('Dataset/not_delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv')
not_delay_7_9 = pd.read_csv('Dataset/not_delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv')
test_data = pd.read_csv('Dataset/PILOT_10.csv')

print("Delay 4-6 columns:", list(delay_4_6.columns))
print("\nDelay 7-9 columns:", list(delay_7_9.columns))
print("\nNot delay 4-6 columns:", list(not_delay_4_6.columns))
print("\nNot delay 7-9 columns:", list(not_delay_7_9.columns))
print("\nTest columns:", list(test_data.columns))

# Check for duplicates in each
print("\n=== Checking duplicates ===")
for name, df in [("delay_4_6", delay_4_6), ("delay_7_9", delay_7_9), 
                 ("not_delay_4_6", not_delay_4_6), ("not_delay_7_9", not_delay_7_9),
                 ("test", test_data)]:
    duplicates = df.columns[df.columns.duplicated()].tolist()
    if duplicates:
        print(f"{name} has duplicates: {duplicates}")
    else:
        print(f"{name} has no duplicates")

# Combine and check
delay_4_6['label'] = 1
delay_7_9['label'] = 1
not_delay_4_6['label'] = 0
not_delay_7_9['label'] = 0

train_data = pd.concat([delay_4_6, delay_7_9, not_delay_4_6, not_delay_7_9], ignore_index=True)
print(f"\nCombined train columns: {list(train_data.columns)}")

duplicates = train_data.columns[train_data.columns.duplicated()].tolist()
if duplicates:
    print(f"Combined train has duplicates: {duplicates}")
else:
    print("Combined train has no duplicates")
