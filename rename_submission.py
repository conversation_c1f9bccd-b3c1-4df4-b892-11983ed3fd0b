#!/usr/bin/env python3
"""
Script để đổi tên file submission theo format yêu cầu
"""

import shutil
import os

def rename_submission_file():
    """Đổi tên file submission theo format yêu cầu"""
    
    # Thông tin cần điền
    print("=== Đổi tên file submission ===")
    print("Vui lòng nhập thông tin:")
    
    mssv = input("MSSV (Mã số sinh viên): ").strip()
    ho_va_ten = input("Họ và tên (không dấu, viết liền): ").strip()
    
    # Score từ CV
    score = "0.9664"  # CV score từ model
    
    # Tên file mới
    new_filename = f"lighgbm-seed-solvers-{mssv}-{ho_va_ten}-{score}.csv"
    
    # Kiểm tra file gốc tồn tại
    if not os.path.exists("submission.csv"):
        print("ERROR: File submission.csv không tồn tại!")
        return
    
    # Copy file với tên mới
    try:
        shutil.copy2("submission.csv", new_filename)
        print(f"✅ Đã tạo file: {new_filename}")
        
        # Kiểm tra file
        with open(new_filename, 'r') as f:
            lines = f.readlines()
            print(f"📊 File có {len(lines)} dòng")
            print(f"📋 Header: {lines[0].strip()}")
            print(f"📋 Sample data: {lines[1].strip()}")
        
        print("\n🎯 File đã sẵn sàng để nộp!")
        print(f"📁 Tên file: {new_filename}")
        
    except Exception as e:
        print(f"ERROR: Không thể tạo file mới: {e}")

if __name__ == "__main__":
    rename_submission_file()
