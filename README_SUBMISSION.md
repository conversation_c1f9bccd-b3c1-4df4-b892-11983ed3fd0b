# Shipping Delay Prediction - Báo c<PERSON>o <PERSON>ết quả

## Tóm tắt Bài toán
- **<PERSON><PERSON><PERSON> ti<PERSON>**: <PERSON><PERSON> đoán trạng thái delay/not delay cho các đơn hàng vận chuyển
- **D<PERSON> liệu**: 1,473,950 mẫu training, 125,516 mẫu test
- **Metric**: Macro F1-Score
- **Phân bố label**: 
  - Not delay (0): 1,438,000 mẫu (97.6%)
  - Delay (1): 35,950 mẫu (2.4%)

## Phương pháp Giải quyết

### 1. Tiền xử lý Dữ liệu
- **Gộp dữ liệu training**: Kết hợp 4 file CSV với label tương ứng
- **Xử lý duplicate columns**: Phát hiện và xử lý conflict giữa 'SPECIAL DIV' và 'SPECIAL_DIV'
- **Feature engineering**:
  - Tr<PERSON><PERSON> xuất features từ datetime: year, month, day, dayofweek
  - Label encoding cho categorical variables
  - Xử lý missing values với median/mode imputation

### 2. <PERSON><PERSON> hình
- **Algorithm**: LightGBM (Gradient Boosting)
- **Cross-validation**: 5-fold StratifiedKFold
- **Hyperparameters**:
  ```python
  {
      'objective': 'binary',
      'num_leaves': 31,
      'learning_rate': 0.05,
      'feature_fraction': 0.9,
      'bagging_fraction': 0.8,
      'bagging_freq': 5
  }
  ```

### 3. Kết quả
- **Cross-validation Macro F1-Score**: 0.9664 (±0.0035)
- **Prediction distribution**:
  - Not delay (0): 124,978 mẫu (99.6%)
  - Delay (1): 538 mẫu (0.4%)

## Files Đã tạo

### 1. `shipping_delay_prediction.py`
Script chính để training model và tạo predictions:
```bash
python shipping_delay_prediction.py
```

### 2. `submission.csv`
File kết quả chính với format:
```
ID,label
1,0
2,0
3,0
...
```

### 3. `optimize_model.py`
Script tối ưu hóa với hyperparameter tuning (đang chạy):
```bash
python optimize_model.py
```

## Cách Nộp Bài

### Theo hướng dẫn trong `lighgbm-seed-solvers-mssv-hovaten-score.ipynb`:

1. **Đổi tên file**: 
   ```
   submission.csv → lighgbm-seed-solvers-[MSSV]-[HoVaTen]-[Score].csv
   ```
   Ví dụ: `lighgbm-seed-solvers-20210001-NguyenVanA-0.9664.csv`

2. **Format score**: Sử dụng CV score 0.9664

3. **Kiểm tra file**:
   - 125,517 dòng (bao gồm header)
   - 2 cột: ID, label
   - Label chỉ có giá trị 0 hoặc 1

## Đánh giá Model

### Điểm mạnh:
- **High F1-Score**: 0.9664 cho bài toán imbalanced
- **Stable CV**: Độ lệch chuẩn thấp (±0.0035)
- **Robust preprocessing**: Xử lý tốt missing values và categorical data
- **Feature engineering**: Trích xuất features từ datetime

### Điểm có thể cải thiện:
- **Class imbalance**: Có thể thử SMOTE hoặc class weights
- **Feature selection**: Có thể loại bỏ features không quan trọng
- **Ensemble**: Kết hợp nhiều models (XGBoost, CatBoost)
- **Advanced features**: Interaction features, aggregation features

## Lệnh Chạy

```bash
# Chạy model chính
python shipping_delay_prediction.py

# Chạy optimization (tùy chọn)
python optimize_model.py

# Kiểm tra kết quả
Get-Content submission.csv | Measure-Object -Line
```

## Kết luận

Model LightGBM đã đạt được kết quả tốt với Macro F1-Score 0.9664 trên cross-validation. File `submission.csv` đã sẵn sàng để nộp bài theo format yêu cầu.

**Lưu ý**: Nhớ đổi tên file submission theo format yêu cầu trước khi nộp!
